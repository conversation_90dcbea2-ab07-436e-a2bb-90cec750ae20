{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/animation/page-transition.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PageTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call PageTransition() from the server but PageTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/animation/page-transition.tsx <module evaluation>\",\n    \"PageTransition\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/animation/page-transition.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PageTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call PageTransition() from the server but PageTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/animation/page-transition.tsx\",\n    \"PageTransition\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/app/page.tsx"], "sourcesContent": ["import { PageTransition } from \"@/components/animation/page-transition\";\nimport Image from \"next/image\";\nimport type { Metadata } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"Bedanta Kataki - Full Stack Developer Portfolio | Home\",\n  description: \"Welcome to Bedanta Kataki's portfolio. Full Stack Developer and Electronics & Instrumentation student at NIT Silchar. Specializing in TypeScript, React, Node.js, and modern web technologies.\",\n  keywords: [\n    \"Bedanta Kataki\",\n    \"Full Stack Developer\",\n    \"Portfolio\",\n    \"NIT Silchar\",\n    \"TypeScript\",\n    \"React\",\n    \"Node.js\",\n    \"Web Development\"\n  ],\n};\n\nexport default function Home() {\n  return (\n    <PageTransition>\n      <section className=\"w-full flex flex-col justify-start p-[0.4rem] mb-8\">\n        <h2 className=\"text-xl sm:text-2xl font-medium mb-4\">Hello ~</h2>\n        <p className=\"mb-4 text-sm sm:text-base\">\n          22 y/o semicolon dev trying to build better web interfaces. taught by\n          the web itself, I write typescript, I love mern stack but I use the{\" \"}\n          <span className=\"text-white hover:opacity-80 transition-opacity underline cursor-pointer\">\n            t3-stack\n          </span>{\" \"}\n          ~ cus{\" \"}\n          <span className=\"text-white hover:opacity-80 transition-opacity underline cursor-pointer\">\n            theo\n          </span>{\" \"}\n          said{\" \"}\n          <mark className=\"text-[hsla(32,98%,83%,.9)] font-thin rounded\">\n            typesafety isn&apos;t optional\n          </mark>{\" \"}\n          apparently &amp; btw I don&apos;t use neovim.\n        </p>\n        <p className=\"mb-4 text-sm sm:text-base\">\n          I&apos;m a fullstack dev, student at{\" \"}\n          <span className=\"text-white hover:opacity-80 transition-opacity underline cursor-pointer\">\n            NIT\n          </span>\n            , oasis fanboy, technical writer (when inspiration hits), shitposter,\n            hackathon grinder and currently on{\" \"}\n            <mark className=\"text-[hsla(32,98%,83%,.9)] font-thin rounded\">\n              Leetcode\n            </mark>\n            {\" \"} grind too. I can write typescript, python, cpp. I enjoy sneaking{\" \"}\n          <mark className=\"text-[hsla(32,98%,83%,.9)] font-thin rounded\">\n            GraphQL{\" \"}\n          </mark>{\" \"}\n          into projects whenever I can ~ I just like when things are structured\n          but flexible.{\" \"}\n          <mark className=\"text-[hsla(32,98%,83%,.9)] font-thin rounded\">\n            System design?{\" \"}\n          </mark>{\" \"}\n          Love it. Nothing beats the thrill of watching complex parts fall into\n          place.\n        </p>\n        <p className=\"mb-0 text-sm sm:text-base\">\n          I like minimalism and simplicity and I try to reflect that in my works\n          as well. I often jam to music while working. Meaningful and deep\n          lyrics is what I look for in songs. My all time fav artist is{\" \"}\n          <span className=\"text-white hover:opacity-80 transition-opacity underline cursor-pointer\">\n            Louis Tomlinson\n          </span>\n          . Apart from this I also like playing FPS games like{\" \"}\n          <span className=\"text-white hover:opacity-80 transition-opacity underline cursor-pointer\">\n            VALORANT\n          </span>\n          , lifting{\" \"}\n          <mark className=\"text-[hsla(32,98%,83%,.9)] font-thin rounded\">\n            weights\n          </mark>\n          , clutching in Table Tennis and binge watching shows on{\" \"}\n          <span className=\"text-white hover:opacity-80 transition-opacity underline cursor-pointer\">\n            netflix\n          </span>\n          .\n        </p>\n      </section>\n\n      {/* Last Played Section */}\n      <div className=\"w-full h-full mb-8\">\n        <div\n          className=\"border border-stone-800/90 p-3 rounded-lg flex flex-row justify-between bg-stone-900/20\"\n          style={{ opacity: 1, transform: \"none\" }}\n        >\n          <div className=\"flex flex-col gap-2 h-20 items-start justify-between\">\n            <p className=\"mb-0 leading-none text-xs sm:text-sm text-zinc-500\">\n              Last played\n            </p>\n            <p className=\"mb-0 leading-none text-sm sm:text-base\">\n              <span className=\"no-underline text-white hover:opacity-80 transition-opacity cursor-pointer\">\n                Line Without a Hook // Montgomery Ricky\n              </span>\n            </p>\n          </div>\n          <div className=\"w-20 h-20 bg-zinc-800/50 rounded-md overflow-hidden\">\n            <Image\n              src=\"https://images.genius.com/c17eacd4676137d5780d7ec97b48d6d1.816x816x1.jpg\"\n              alt=\"Album cover\"\n              width={128}\n              height={128}\n              className=\"w-full h-full object-cover\"\n              unoptimized\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Social Links Section */}\n      <section className=\"w-full flex flex-col justify-start p-[0.4rem]\">\n        <h2 className=\"text-xl sm:text-2xl font-medium mb-6\">Find me here ~</h2>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 w-full gap-y-6 gap-x-12\">\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">GitHub</p>\n            <a\n              href=\"https://github.com/r4inr3aper\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n              target=\"_blank\"\n            >\n              @r4inr3aper\n            </a>\n          </div>\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">Leetcode</p>\n            <a\n              href=\"https://leetcode.com/u/xbedanta/\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n              target=\"_blank\"\n            >\n              @xbedanta\n            </a>\n          </div>\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">\n              X (formerly Twitter)\n            </p>\n            <a\n              href=\"https://x.com/bedantaxdev\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n              target=\"_blank\"\n            >\n              @bedantaxdev\n            </a>\n          </div>\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">LinkedIn</p>\n            <a\n              href=\"https://www.linkedin.com/in/bedanta-kataki-0b5205257/\"\n              target=\"_blank\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n            >\n              @bedanta\n            </a>\n          </div>\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">Email</p>\n            <a\n              href=\"mailto:<EMAIL>\"\n              target=\"_blank\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n            >\n              <EMAIL>\n            </a>\n          </div>\n          {/* <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">CV</p>\n            <a\n              href=\"https://read.cv/bedanta\"\n              target=\"_blank\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n            >\n              read.cv/bedanta\n            </a>\n          </div> */}\n        </div>\n      </section>\n    </PageTransition>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEe,SAAS;IACtB,qBACE,8OAAC,qJAAA,CAAA,iBAAc;;0BACb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAE,WAAU;;4BAA4B;4BAE6B;0CACpE,8OAAC;gCAAK,WAAU;0CAA0E;;;;;;4BAElF;4BAAI;4BACN;0CACN,8OAAC;gCAAK,WAAU;0CAA0E;;;;;;4BAElF;4BAAI;4BACP;0CACL,8OAAC;gCAAK,WAAU;0CAA+C;;;;;;4BAEvD;4BAAI;;;;;;;kCAGd,8OAAC;wBAAE,WAAU;;4BAA4B;4BACF;0CACrC,8OAAC;gCAAK,WAAU;0CAA0E;;;;;;4BAEnF;4BAE8B;0CACnC,8OAAC;gCAAK,WAAU;0CAA+C;;;;;;4BAG9D;4BAAI;4BAAkE;0CACzE,8OAAC;gCAAK,WAAU;;oCAA+C;oCACrD;;;;;;;4BACF;4BAAI;4BAEE;0CACd,8OAAC;gCAAK,WAAU;;oCAA+C;oCAC9C;;;;;;;4BACT;4BAAI;;;;;;;kCAId,8OAAC;wBAAE,WAAU;;4BAA4B;4BAGuB;0CAC9D,8OAAC;gCAAK,WAAU;0CAA0E;;;;;;4BAEnF;4BAC8C;0CACrD,8OAAC;gCAAK,WAAU;0CAA0E;;;;;;4BAEnF;4BACG;0CACV,8OAAC;gCAAK,WAAU;0CAA+C;;;;;;4BAExD;4BACiD;0CACxD,8OAAC;gCAAK,WAAU;0CAA0E;;;;;;4BAEnF;;;;;;;;;;;;;0BAMX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,SAAS;wBAAG,WAAW;oBAAO;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CACX,cAAA,8OAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;;;;;;sCAKjG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,WAAW;;;;;;;;;;;;;;;;;;;;;;0BAOnB,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAO;kDACR;;;;;;;;;;;;0CAIH,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAO;kDACR;;;;;;;;;;;;0CAIH,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;kDAGxD,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAO;kDACR;;;;;;;;;;;;0CAIH,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBb", "debugId": null}}]}