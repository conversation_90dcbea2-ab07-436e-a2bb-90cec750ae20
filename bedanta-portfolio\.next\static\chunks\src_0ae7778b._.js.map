{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/animation/page-transition.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { ReactNode } from \"react\";\n\ninterface PageTransitionProps {\n  children: ReactNode;\n}\n\nexport function PageTransition({ children }: PageTransitionProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 10 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: 10 }}\n      transition={{\n        duration: 0.3,\n        ease: \"easeInOut\",\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG;QAAG;QAC1B,YAAY;YACV,UAAU;YACV,MAAM;QACR;kBAEC;;;;;;AAGP;KAdgB", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { CheckCircle, XCircle, X } from \"lucide-react\";\r\n\r\ninterface ToastProps {\r\n  message: string;\r\n  type: \"success\" | \"error\";\r\n  isVisible: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport function Toast({ message, type, isVisible, onClose }: ToastProps) {\r\n  const [isAnimating, setIsAnimating] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (isVisible) {\r\n      setIsAnimating(true);\r\n      const timer = setTimeout(() => {\r\n        onClose();\r\n      }, 5000); // Auto close after 5 seconds\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [isVisible, onClose]);\r\n\r\n  if (!isVisible) return null;\r\n\r\n  const icon = type === \"success\" ? (\r\n    <CheckCircle className=\"w-5 h-5 text-green-400\" />\r\n  ) : (\r\n    <XCircle className=\"w-5 h-5 text-red-400\" />\r\n  );\r\n\r\n  const bgColor = type === \"success\" \r\n    ? \"bg-green-900/90 border-green-700\" \r\n    : \"bg-red-900/90 border-red-700\";\r\n\r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 z-50 animate-in slide-in-from-bottom-2 duration-300\">\r\n      <div className={`${bgColor} border rounded-lg p-4 shadow-lg backdrop-blur-sm min-w-[300px]`}>\r\n        <div className=\"flex items-start gap-3\">\r\n          {icon}\r\n          <div className=\"flex-1\">\r\n            <p className=\"text-white text-sm font-medium\">\r\n              {type === \"success\" ? \"Success!\" : \"Error\"}\r\n            </p>\r\n            <p className=\"text-gray-300 text-sm mt-1\">{message}</p>\r\n          </div>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-gray-400 hover:text-white transition-colors\"\r\n          >\r\n            <X className=\"w-4 h-4\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAYO,SAAS,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAc;;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,WAAW;gBACb,eAAe;gBACf,MAAM,QAAQ;6CAAW;wBACvB;oBACF;4CAAG,OAAO,6BAA6B;gBAEvC;uCAAO,IAAM,aAAa;;YAC5B;QACF;0BAAG;QAAC;QAAW;KAAQ;IAEvB,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,OAAO,SAAS,0BACpB,6LAAC,8NAAA,CAAA,cAAW;QAAC,WAAU;;;;;6BAEvB,6LAAC,+MAAA,CAAA,UAAO;QAAC,WAAU;;;;;;IAGrB,MAAM,UAAU,SAAS,YACrB,qCACA;IAEJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAW,GAAG,QAAQ,+DAA+D,CAAC;sBACzF,cAAA,6LAAC;gBAAI,WAAU;;oBACZ;kCACD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,SAAS,YAAY,aAAa;;;;;;0CAErC,6LAAC;gCAAE,WAAU;0CAA8B;;;;;;;;;;;;kCAE7C,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;GA/CgB;KAAA", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/app/contact/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { PageTransition } from \"@/components/animation/page-transition\";\nimport { Toast } from \"@/components/ui/toast\";\nimport { useState } from \"react\";\n\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\",\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [toast, setToast] = useState<{\n    isVisible: boolean;\n    message: string;\n    type: \"success\" | \"error\";\n  }>({\n    isVisible: false,\n    message: \"\",\n    type: \"success\",\n  });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    try {\n      const response = await fetch(\"https://formspree.io/f/mwpqeaqw\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(formData),\n      });\n\n      if (response.ok) {\n        setToast({\n          isVisible: true,\n          message: \"Message sent successfully! I'll get back to you soon.\",\n          type: \"success\",\n        });\n        setFormData({ name: \"\", email: \"\", message: \"\" });\n      } else {\n        setToast({\n          isVisible: true,\n          message: \"Something went wrong. Please try again or email me directly.\",\n          type: \"error\",\n        });\n      }\n    } catch (error) {\n      setToast({\n        isVisible: true,\n        message: \"Something went wrong. Please try again or email me directly.\",\n        type: \"error\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  return (\n    <PageTransition>\n      {/* Header */}\n      <section className=\"w-full flex flex-col justify-start p-[0.4rem] mb-4\">\n        <div className=\"flex items-center gap-3 mb-4\">\n          <h2 className=\"text-xl sm:text-2xl font-medium\">Contact ~</h2>\n          <div className=\"flex items-center gap-1.5 bg-green-500/10 border border-green-500/20 px-2 py-1 rounded-full\">\n            <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full animate-bounce\"></div>\n            <span className=\"text-xs text-green-400 font-medium\">LIVE</span>\n          </div>\n        </div>\n        <p className=\"text-sm sm:text-base text-zinc-300\">\n          Got a project, a question, or just wanna vibe? I'm always down to chat.\n          Hit me up through the form or connect on any platform below.\n        </p>\n      </section>\n\n      {/* Form */}\n      <section className=\"w-full flex flex-col justify-start p-[0.4rem] mb-12\">\n        <form onSubmit={handleSubmit} className=\"w-full max-w-2xl space-y-6\">\n          <div className=\"flex flex-col\">\n            <label htmlFor=\"name\" className=\"text-sm text-zinc-400 mb-1\">\n              Name\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleInputChange}\n              required\n              placeholder=\"Jane Doe\"\n              className=\"bg-stone-900/40 border border-stone-800 rounded-lg px-4 py-2 text-white placeholder-zinc-500 outline-none focus:ring-2 focus:ring-stone-700\"\n            />\n          </div>\n\n          <div className=\"flex flex-col\">\n            <label htmlFor=\"email\" className=\"text-sm text-zinc-400 mb-1\">\n              Email\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              required\n              placeholder=\"<EMAIL>\"\n              className=\"bg-stone-900/40 border border-stone-800 rounded-lg px-4 py-2 text-white placeholder-zinc-500 outline-none focus:ring-2 focus:ring-stone-700\"\n            />\n          </div>\n\n          <div className=\"flex flex-col\">\n            <label htmlFor=\"message\" className=\"text-sm text-zinc-400 mb-1\">\n              Message\n            </label>\n            <textarea\n              id=\"message\"\n              name=\"message\"\n              value={formData.message}\n              onChange={handleInputChange}\n              rows={5}\n              required\n              placeholder=\"What's on your mind?\"\n              className=\"bg-stone-900/40 border border-stone-800 rounded-lg px-4 py-2 text-white placeholder-zinc-500 outline-none focus:ring-2 focus:ring-stone-700 resize-none\"\n            ></textarea>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"bg-stone-800 hover:bg-stone-700 disabled:bg-stone-900 disabled:cursor-not-allowed transition-colors rounded-lg px-4 py-2 text-white font-medium\"\n          >\n            {isSubmitting ? \"Sending...\" : \"Send Message\"}\n          </button>\n        </form>\n      </section>\n\n      {/* Toast */}\n      <Toast\n        isVisible={toast.isVisible}\n        message={toast.message}\n        type={toast.type}\n        onClose={() => setToast(prev => ({ ...prev, isVisible: false }))}\n      />\n\n      {/* Socials */}\n      <section className=\"w-full flex flex-col justify-start p-[0.4rem]\">\n        <h3 className=\"text-lg sm:text-xl font-medium mb-6\">Find me here ~</h3>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 w-full gap-y-6 gap-x-12\">\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">Email</p>\n            <a\n              href=\"mailto:<EMAIL>\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n              target=\"_blank\"\n            >\n              <EMAIL>\n            </a>\n          </div>\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">GitHub</p>\n            <a\n              href=\"https://github.com/r4inr3aper\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n              target=\"_blank\"\n            >\n              @r4inr3aper\n            </a>\n          </div>\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">Leetcode</p>\n            <a\n              href=\"https://leetcode.com/u/xbedanta/\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n              target=\"_blank\"\n            >\n              @r4inr3aper\n            </a>\n          </div>\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">LinkedIn</p>\n            <a\n              href=\"https://www.linkedin.com/in/bedanta-kataki-0b5205257/\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n              target=\"_blank\"\n            >\n              @bedanta\n            </a>\n          </div>\n          <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">X (formerly Twitter)</p>\n            <a\n              href=\"https://x.com/bedantaxdev\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n              target=\"_blank\"\n            >\n              @bedantaxdev\n            </a>\n          </div>\n          {/* <div>\n            <p className=\"mb-2 text-zinc-400 font-semibold text-sm\">CV</p>\n            <a\n              href=\"https://read.cv/bedanta\"\n              className=\"text-white hover:opacity-80 transition-opacity underline\"\n              target=\"_blank\"\n            >\n              read.cv/bedanta\n            </a>\n          </div> */}\n        </div>\n      </section>\n    </PageTransition>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAI9B;QACD,WAAW;QACX,SAAS;QACT,MAAM;IACR;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS;oBACP,WAAW;oBACX,SAAS;oBACT,MAAM;gBACR;gBACA,YAAY;oBAAE,MAAM;oBAAI,OAAO;oBAAI,SAAS;gBAAG;YACjD,OAAO;gBACL,SAAS;oBACP,WAAW;oBACX,SAAS;oBACT,MAAM;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,SAAS;gBACP,WAAW;gBACX,SAAS;gBACT,MAAM;YACR;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,qBACE,6LAAC,wJAAA,CAAA,iBAAc;;0BAEb,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;0BAOpD,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAA6B;;;;;;8CAG7D,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,QAAQ;oCACR,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA6B;;;;;;8CAG9D,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,QAAQ;oCACR,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAU,WAAU;8CAA6B;;;;;;8CAGhE,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,MAAM;oCACN,QAAQ;oCACR,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAId,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,eAAe,eAAe;;;;;;;;;;;;;;;;;0BAMrC,6LAAC,oIAAA,CAAA,QAAK;gBACJ,WAAW,MAAM,SAAS;gBAC1B,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,SAAS,IAAM,SAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,WAAW;wBAAM,CAAC;;;;;;0BAIhE,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAO;kDACR;;;;;;;;;;;;0CAIH,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAO;kDACR;;;;;;;;;;;;0CAIH,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAO;kDACR;;;;;;;;;;;;0CAIH,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAO;kDACR;;;;;;;;;;;;0CAIH,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAO;kDACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBb;GAtNwB;KAAA", "debugId": null}}]}