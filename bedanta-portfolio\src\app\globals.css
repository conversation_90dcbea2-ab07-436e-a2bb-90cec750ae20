@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-gradient-to-tr from-zinc-950 via-stone-900 to-neutral-950 text-zinc-100 min-h-screen font-sans overflow-x-hidden min-w-full;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-medium text-zinc-100;
  }

  h2 {
    @apply text-2xl;
  }

  a {
    @apply text-zinc-100 hover:text-zinc-200 transition-colors duration-300;
  }

  mark {
    @apply bg-transparent text-zinc-100 font-semibold;
  }
}

/* Custom font imports from the same-assets URLs */
@font-face {
  font-family: "GeneralSans";
  src: url("https://ext.same-assets.com/3005247063/2545564193.woff2")
    format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "GeneralSans";
  src: url("https://ext.same-assets.com/3005247063/522942080.woff2")
    format("woff2");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "GeneralSans";
  src: url("https://ext.same-assets.com/3005247063/2849113394.woff2")
    format("woff2");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "GeneralSans";
  src: url("https://ext.same-assets.com/3005247063/234977249.woff2")
    format("woff2");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}@keyframes expandWidth {
  from { transform: scaleX(0); }
  to { transform: scaleX(1); }
}

.animate-expandWidth {
  animation: expandWidth 0.1s forwards;
}
