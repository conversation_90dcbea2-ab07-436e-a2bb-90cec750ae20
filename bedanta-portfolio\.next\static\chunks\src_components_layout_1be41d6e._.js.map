{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { FileText } from \"lucide-react\";\r\n\r\nexport function Header() {\r\n  const pathname = usePathname();\r\n  const indicatorRef = useRef<HTMLDivElement>(null);\r\n  const navRef = useRef<HTMLDivElement>(null);\r\n  const [hoveredItem, setHoveredItem] = useState<string | null>(null);\r\n\r\n  const navItems = [\r\n    { label: \"Home\", path: \"/\" },\r\n    { label: \"About\", path: \"/about\" },\r\n    { label: \"Projects\", path: \"/projects\" },\r\n    { label: \"Contact\", path: \"/contact\" },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (!navRef.current || !indicatorRef.current) return;\r\n\r\n    const activeLink = navRef.current.querySelector<HTMLAnchorElement>('[data-active=\"true\"]');\r\n\r\n    if (activeLink) {\r\n      const { offsetLeft, offsetWidth } = activeLink;\r\n\r\n      requestAnimationFrame(() => {\r\n        if (indicatorRef.current) {\r\n          indicatorRef.current.style.transition = 'width 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275), transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease';\r\n          indicatorRef.current.style.width = `${offsetWidth}px`;\r\n          indicatorRef.current.style.transform = `translateX(${offsetLeft}px)`;\r\n          indicatorRef.current.style.opacity = '1';\r\n        }\r\n      });\r\n    }\r\n  }, [pathname]);\r\n\r\n  const handleMouseEnter = (path: string) => {\r\n    setHoveredItem(path);\r\n\r\n    if (!navRef.current || !indicatorRef.current) return;\r\n\r\n    const hoveredLink = navRef.current.querySelector<HTMLAnchorElement>(`[href=\"${path}\"]`);\r\n\r\n    if (hoveredLink) {\r\n      const { offsetLeft, offsetWidth } = hoveredLink;\r\n\r\n      requestAnimationFrame(() => {\r\n        if (indicatorRef.current) {\r\n          indicatorRef.current.style.transition = 'width 0.35s cubic-bezier(0.34, 1.56, 0.64, 1), transform 0.35s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.2s ease';\r\n          indicatorRef.current.style.width = `${offsetWidth}px`;\r\n          indicatorRef.current.style.transform = `translateX(${offsetLeft}px)`;\r\n          indicatorRef.current.style.opacity = '1';\r\n        }\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleMouseLeave = () => {\r\n    setHoveredItem(null);\r\n\r\n    if (!navRef.current || !indicatorRef.current) return;\r\n\r\n    const activeLink = navRef.current.querySelector<HTMLAnchorElement>('[data-active=\"true\"]');\r\n\r\n    if (activeLink) {\r\n      const { offsetLeft, offsetWidth } = activeLink;\r\n\r\n      requestAnimationFrame(() => {\r\n        if (indicatorRef.current) {\r\n          indicatorRef.current.style.transition = 'width 0.4s cubic-bezier(0.25, 0.1, 0.25, 1), transform 0.4s cubic-bezier(0.25, 0.1, 0.25, 1), opacity 0.2s ease';\r\n          indicatorRef.current.style.width = `${offsetWidth}px`;\r\n          indicatorRef.current.style.transform = `translateX(${offsetLeft}px)`;\r\n        }\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Profile Section */}\r\n      <section className=\"w-full flex gap-4 justify-between mb-6 p-2\">\r\n        <div className=\"flex gap-4\">\r\n          <img\r\n            src=\"./me.jpeg\"\r\n            alt=\"bedanta\"\r\n            width={60}\r\n            height={60}\r\n            className=\"rounded-full w-14 h-14 object-cover transition-transform hover:scale-105 duration-300\"\r\n          />\r\n          <div className=\"flex flex-col gap-2 justify-center\">\r\n            <h2 className=\"mb-0\">bedanta</h2>\r\n            <p className=\"mb-0 text-zinc-400 font-normal leading-none\">\r\n              Student • Dev • Ailurophile\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Resume Button */}\r\n        <div className=\"flex items-center\">\r\n          <a\r\n            href=\"https://drive.google.com/file/d/15bivsiGDuSctgDFQa2wb06W-ExTvtBTQ/view?usp=sharing\"\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            className=\"group flex items-center gap-2 px-3 py-2 bg-stone-800/80 hover:bg-stone-800/90 border border-stone-700/50 hover:border-stone-700/70 rounded-lg text-sm font-normal text-zinc-100 transition-all duration-300 hover:shadow-md no-underline\"\r\n          >\r\n            <FileText size={16} className=\"transition-transform duration-300 group-hover:rotate-3\" />\r\n            <span className=\"hidden sm:inline\">Resume</span>\r\n          </a>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"border border-stone-800/90 p-[0.4rem] rounded-lg mb-12 sticky top-4 z-[100] bg-stone-900/80 backdrop-blur-md\">\r\n        <nav\r\n          ref={navRef}\r\n          className=\"flex gap-2 relative justify-start w-full z-[100] rounded-lg\"\r\n          onMouseLeave={handleMouseLeave}\r\n        >\r\n          {/* Active indicator background */}\r\n          <div\r\n            ref={indicatorRef}\r\n            className=\"absolute h-[calc(100%-0.5rem)] bg-stone-800 rounded-md top-1 z-0 opacity-0\"\r\n            style={{\r\n              transition: 'width 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275), transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease',\r\n              willChange: 'transform, width, opacity'\r\n            }}\r\n          />\r\n\r\n          {/* Navigation items */}\r\n          {navItems.map((item) => (\r\n            <Link\r\n              key={item.path}\r\n              href={item.path}\r\n              className={`px-4 py-3 border-1 border-gray-100 rounded-md text-sm relative no-underline transition-colors duration-200 ease-in z-10 \r\n                ${pathname === item.path\r\n                  ? \"text-zinc-100\"\r\n                  : hoveredItem === item.path\r\n                    ? \"text-zinc-200\"\r\n                    : \"text-zinc-400\"}`}\r\n              data-active={pathname === item.path}\r\n              onMouseEnter={() => handleMouseEnter(item.path)}\r\n            >\r\n              <span>{item.label}</span>\r\n            </Link>\r\n          ))}\r\n        </nav>\r\n      </div>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,WAAW;QACf;YAAE,OAAO;YAAQ,MAAM;QAAI;QAC3B;YAAE,OAAO;YAAS,MAAM;QAAS;QACjC;YAAE,OAAO;YAAY,MAAM;QAAY;QACvC;YAAE,OAAO;YAAW,MAAM;QAAW;KACtC;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,aAAa,OAAO,EAAE;YAE9C,MAAM,aAAa,OAAO,OAAO,CAAC,aAAa,CAAoB;YAEnE,IAAI,YAAY;gBACd,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;gBAEpC;wCAAsB;wBACpB,IAAI,aAAa,OAAO,EAAE;4BACxB,aAAa,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG;4BACxC,aAAa,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,YAAY,EAAE,CAAC;4BACrD,aAAa,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,GAAG,CAAC;4BACpE,aAAa,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;wBACvC;oBACF;;YACF;QACF;2BAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAC;QACxB,eAAe;QAEf,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,aAAa,OAAO,EAAE;QAE9C,MAAM,cAAc,OAAO,OAAO,CAAC,aAAa,CAAoB,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;QAEtF,IAAI,aAAa;YACf,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;YAEpC,sBAAsB;gBACpB,IAAI,aAAa,OAAO,EAAE;oBACxB,aAAa,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG;oBACxC,aAAa,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,YAAY,EAAE,CAAC;oBACrD,aAAa,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,GAAG,CAAC;oBACpE,aAAa,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;gBACvC;YACF;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,eAAe;QAEf,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,aAAa,OAAO,EAAE;QAE9C,MAAM,aAAa,OAAO,OAAO,CAAC,aAAa,CAAoB;QAEnE,IAAI,YAAY;YACd,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;YAEpC,sBAAsB;gBACpB,IAAI,aAAa,OAAO,EAAE;oBACxB,aAAa,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG;oBACxC,aAAa,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,YAAY,EAAE,CAAC;oBACrD,aAAa,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,GAAG,CAAC;gBACtE;YACF;QACF;IACF;IAEA,qBACE;;0BAEE,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAO;;;;;;kDACrB,6LAAC;wCAAE,WAAU;kDAA8C;;;;;;;;;;;;;;;;;;kCAO/D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,QAAO;4BACP,KAAI;4BACJ,WAAU;;8CAEV,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC9B,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,KAAK;oBACL,WAAU;oBACV,cAAc;;sCAGd,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,YAAY;4BACd;;;;;;wBAID,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC;gBACV,EAAE,aAAa,KAAK,IAAI,GACpB,kBACA,gBAAgB,KAAK,IAAI,GACvB,kBACA,iBAAiB;gCACzB,eAAa,aAAa,KAAK,IAAI;gCACnC,cAAc,IAAM,iBAAiB,KAAK,IAAI;0CAE9C,cAAA,6LAAC;8CAAM,KAAK,KAAK;;;;;;+BAXZ,KAAK,IAAI;;;;;;;;;;;;;;;;;;AAkB5B;GAjJgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/layout/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\n\nexport function Footer() {\n  const [formattedTime, setFormattedTime] = useState(\"\");\n\n  useEffect(() => {\n    const updateTime = () => {\n      const now = new Date();\n      const formatted = now.toLocaleString(\"en-US\", {\n        month: \"short\",\n        day: \"2-digit\",\n        year: \"numeric\",\n        hour: \"numeric\",\n        minute: \"2-digit\",\n        second: \"2-digit\",\n        hour12: true,\n      });\n      setFormattedTime(formatted);\n    };\n\n    updateTime(); // Set immediately on mount\n    const interval = setInterval(updateTime, 1000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return (\n    <footer className=\"flex px-4 py-4 justify-center gap-6 items-center w-full border-t border-stone-800/90\">\n      <div className=\"w-full lg:w-[55%] flex justify-between\">\n        <p className=\"leading-none m-0\">made by bedanta</p>\n        <p className=\"leading-none m-0 text-[0.90rem]\">{formattedTime}</p>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIO,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;+CAAa;oBACjB,MAAM,MAAM,IAAI;oBAChB,MAAM,YAAY,IAAI,cAAc,CAAC,SAAS;wBAC5C,OAAO;wBACP,KAAK;wBACL,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,QAAQ;wBACR,QAAQ;oBACV;oBACA,iBAAiB;gBACnB;;YAEA,cAAc,2BAA2B;YACzC,MAAM,WAAW,YAAY,YAAY;YAEzC;oCAAO,IAAM,cAAc;;QAC7B;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAAmB;;;;;;8BAChC,6LAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;;;;;;AAIxD;GAhCgB;KAAA", "debugId": null}}]}