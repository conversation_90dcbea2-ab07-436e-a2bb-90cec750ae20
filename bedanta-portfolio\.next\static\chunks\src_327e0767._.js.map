{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/animation/page-transition.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { ReactNode } from \"react\";\n\ninterface PageTransitionProps {\n  children: ReactNode;\n}\n\nexport function PageTransition({ children }: PageTransitionProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 10 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: 10 }}\n      transition={{\n        duration: 0.3,\n        ease: \"easeInOut\",\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG;QAAG;QAC1B,YAAY;YACV,UAAU;YACV,MAAM;QACR;kBAEC;;;;;;AAGP;KAdgB", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/app/about/page.tsx"], "sourcesContent": ["'use client'\nimport { useState } from \"react\";\nimport { PageTransition } from \"@/components/animation/page-transition\";\nimport { ChevronDown, ChevronUp } from \"lucide-react\";\n\nexport default function About() {\n  const [expandedExperiences, setExpandedExperiences] = useState<Record<string, boolean>>({});\n\n  const toggleExpand = (id: string) => {\n    setExpandedExperiences(prev => ({\n      ...prev,\n      [id]: !prev[id]\n    }));\n  };\n\n  const skills = {\n    Languages: [\"C/C++\", \"JavaScript\", \"TypeScript\", \"Python\", \"HTML\", \"CSS\"],\n    Databases: [\n      \"MongoDB\",\n      \"DynamoDB\",\n      \"SQL\",\n      \"QdrantDB\",\n      \"Neo4j\",\n      \"Pinecone\",\n      \"PGVector\",\n      \"PostgreSQL\",\n      \"Redis\",\n    ],\n    Frameworks: [\"Express.js\", \"React.js\", \"Langchain.js\", \"Node.js\"],\n    \"Tools & Technologies\": [\n      \"Git\",\n      \"REST API\",\n      \"GraphQL\",\n      \"Serverless\",\n      \"Docker\",\n      \"AWS\",\n      \"WebSockets\",\n      \"Vercel\",\n      \"Power BI\",\n      \"MS Excel\",\n      \"VS Code\",\n    ],\n  };\n\n  const experiences = [\n    {\n      id: \"procurpal\",\n      company: \"ProcUrPal\",\n      role: \"Full Stack Developer Intern\",\n      period: \"Dec 2024 - Apr 2025\",\n      logo: \"/procurpal.png\",\n      description: \"Worked on building an E-Auction Platform with Next.js, TypeScript and Node.js. Working on both frontend and backend systems, implementing database solutions and API integrations.\",\n      achievements: [\n        \"Developed dynamic dashboards for the eAuction module and integrated it into the existing RFX flow, enabling seamless multi-step auction workflows.\",\n        \"Integrated QSign Digital Signature API to provide legally binding e-signatures with robust audit trails and automated approval workflows.\",\n        \"Resolved bugs in the legacy RFX module and implemented WebSocket based real-time chat, improving responsiveness and overall user experience.\"\n      ],\n      link: \"https://procurpal.in/\"\n    },\n    {\n      id: \"gdsc\",\n      company: \"Google Developers Students Clubs, NIT Silchar\",\n      role: \"Core Member\",\n      period: \"May 2023 - May 2025\",\n      logo: \"https://students.engineering.asu.edu/wp-content/uploads/2023/06/GDSC-Crop.png\",\n      description: \"Contributed to various projects and events organized by the club. Engaged in community building and knowledge sharing sessions. Actively participated in workshops and hackathons.\",\n      achievements: [\n        \"Organized and led technical workshops like Web Blitz 4.0 with 200+ attendees.\",\n        \"Led a development team, conducted code reviews and resolved front-end and back-end issues to ensure smooth project execution.\",\n        \"Mentored junior members in web development and project management best practices.\",\n      ],\n      link: \"https://gdscnits.in/\"\n    }\n  ];\n\n  return (\n    <PageTransition>\n      {/* About Section */}\n      <section className=\"w-full flex flex-col justify-start p-[0.4rem] mb-8\">\n        <h2 className=\"text-xl sm:text-2xl font-medium mb-4\">About ~</h2>\n        <p className=\"mb-4 text-sm sm:text-base\">\n          Crying over college assignments, trying to hit the gym consistently and\n          trying to get familiar with{\" \"}\n          <mark className=\"text-[hsla(32,98%,83%,.9)] font-thin rounded\">\n            dsa\n          </mark>{\" \"}\n          and{\" \"}\n          <mark className=\"text-[hsla(32,98%,83%,.9)] font-thin rounded\">\n            cpp (oops)\n          </mark>.\n        </p>\n        <p className=\"mb-4 text-sm sm:text-base\">\n          I&apos;m confident enough with my web dev skills right now (still a noob\n          though). Lately, I’ve been fascinated by GenAI and experimenting with it to bring fresh ideas to life.\n        </p>\n        <p className=\"mb-0 text-sm sm:text-base\">\n          Also exploring system design to build scalable apps and focusing on writing clean, production-level code.\n        </p>\n      </section>\n\n      {/* Education Section */}\n      <section className=\"w-full flex flex-col justify-start p-[0.4rem] mb-8\">\n        <h2 className=\"text-xl sm:text-2xl font-medium mb-4\">Education ~</h2>\n        \n        {/* Education Card */}\n        <div \n          className=\"border border-stone-800/90 p-6 rounded-lg bg-stone-900/30 hover:bg-stone-900/40 transition-all duration-300 shadow-md hover:shadow-lg\"\n          style={{ opacity: 1, transform: \"none\" }}\n        >\n          <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-4\">\n            <div className=\"w-16 h-16 bg-white p-[4px] rounded-md overflow-hidden flex items-center justify-center shrink-0\">\n              <img\n                src=\"https://gyaanarth.com/wp-content/uploads/2022/09/logo.jpg\"\n                alt=\"College logo\"\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n            <div className=\"flex-grow\">\n                <h3 className=\"text-xl font-medium\">\n                <a\n                  href=\"https://www.nits.ac.in\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  National Institute of Technology, Silchar\n                </a>\n                </h3>\n              <div className=\"flex flex-col sm:flex-row sm:gap-4 mt-2\">\n                <span className=\"text-sm text-zinc-300 inline-flex items-center\">\n                  <svg className=\"w-4 h-4 mr-1.5 shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"></path>\n                  </svg>\n                  <span className=\"flex-shrink\">B.Tech in Electronics and Instrumentation</span>\n                </span>\n                <span className=\"text-sm text-zinc-300 inline-flex items-center mt-1 sm:mt-0\">\n                  <svg className=\"w-4 h-4 mr-1.5 shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n                  </svg>\n                  2022 - 2026\n                </span>\n              </div>\n            </div>\n          </div>\n          <p className=\"text-sm text-zinc-200 mt-2 leading-relaxed\">\n            Currently pursuing my bachelor&apos;s degree with a focus on full-stack development and \n            modern web technologies. Actively participating in hackathons and tech events.\n          </p>\n        </div>\n      </section>\n\n      {/* Experience Section */}\n      <section className=\"w-full flex flex-col justify-start p-[0.4rem] mb-8\">\n        <h2 className=\"text-xl sm:text-2xl font-medium mb-4\">Experience ~</h2>\n        \n        {/* Experience Cards */}\n        <div className=\"space-y-6\">\n          {experiences.map((exp) => (\n            <div \n              key={exp.id}\n              className=\"border border-stone-800/90 p-6 rounded-lg bg-stone-900/30 hover:bg-stone-900/40 transition-all duration-300 shadow-md hover:shadow-lg\"\n            >\n              <div className=\"flex flex-col sm:flex-row w-full\">\n                <div className=\"flex items-start gap-4 mb-4 flex-grow\">\n                  <div className=\"w-16 h-16 bg-zinc-800/50 rounded-md overflow-hidden flex items-center justify-center shrink-0\">\n                    <img\n                      src={exp.logo}\n                      alt={`${exp.company} logo`}\n                      className=\"w-full h-full p-[5px] object-contain\"\n                    />\n                  </div>\n                  <div className=\"flex-grow\">\n                    <div className=\"flex items-start justify-between\">\n                        <h3\n                        className=\"text-xl font-medium cursor-pointer\"\n                        onClick={() => window.open(exp.link, \"_blank\", \"noopener,noreferrer\")}\n                        tabIndex={0}\n                        role=\"button\"\n                        aria-label={`Open ${exp.company} website`}\n                        onKeyDown={e => {\n                          if (e.key === \"Enter\" || e.key === \" \") {\n                          window.open(exp.link, \"_blank\", \"noopener,noreferrer\");\n                          }\n                        }}\n                        >\n                        {exp.company}\n                        </h3>\n                      <button \n                        onClick={() => toggleExpand(exp.id)} \n                        className=\"ml-2 p-2 rounded-full hover:bg-stone-800/50 transition-colors shrink-0 sm:hidden\"\n                        aria-label={expandedExperiences[exp.id] ? \"Collapse details\" : \"Expand details\"}\n                      >\n                        {expandedExperiences[exp.id] ? (\n                          <ChevronUp className=\"w-5 h-5\" />\n                        ) : (\n                          <ChevronDown className=\"w-5 h-5\" />\n                        )}\n                      </button>\n                    </div>\n                    <div className=\"flex flex-col sm:flex-row sm:gap-4 mt-2\">\n                      <span className=\"text-sm text-zinc-300 inline-flex items-center\">\n                        <svg className=\"w-4 h-4 mr-1.5 shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"></path>\n                        </svg>\n                        {exp.role}\n                      </span>\n                      <span className=\"text-sm text-zinc-300 inline-flex items-center mt-1 sm:mt-0\">\n                        <svg className=\"w-4 h-4 mr-1.5 shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n                        </svg>\n                        {exp.period}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                <button \n                  onClick={() => toggleExpand(exp.id)} \n                  className=\"hidden sm:block p-2 rounded-full transition-colors shrink-0\"\n                  aria-label={expandedExperiences[exp.id] ? \"Collapse details\" : \"Expand details\"}\n                >\n                  {expandedExperiences[exp.id] ? (\n                    <ChevronUp className=\"w-5 h-5\" />\n                  ) : (\n                    <ChevronDown className=\"w-5 h-5\" />\n                  )}\n                </button>\n              </div>\n              <p className=\"text-sm text-zinc-200 mt-2 leading-relaxed\">\n                {exp.description}\n              </p>\n              \n              {/* Collapsible Content */}\n              {expandedExperiences[exp.id] && (\n                <div className=\"mt-4 pt-4 border-t border-stone-800/60 animate-slideDown\">\n                  <h4 className=\"font-medium text-sm mb-2 text-zinc-300\">Key Achievements</h4>\n                  <ul className=\"list-disc list-inside space-y-1 text-sm text-zinc-300\">\n                    {exp.achievements.map((achievement, index) => (\n                      <li key={index} className=\"pl-1\">{achievement}</li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </section>\n\n      {/* Skills Section */}\n      <section className=\"w-full flex flex-col justify-start p-[0.4rem] mb-8\">\n        <h2 className=\"text-xl sm:text-2xl font-medium mb-4\">Skills ~</h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          {Object.entries(skills).map(([category, skillList]) => (\n            <div key={category} className=\"mb-6\">\n              <h3 className=\"text-lg font-medium mb-4 text-zinc-200\">{category}</h3>\n              <div className=\"flex flex-wrap gap-2\">\n                {skillList.map((skill) => (\n                  <span\n                    key={skill}\n                    className=\"px-3.5 py-1.5 bg-stone-800/80 text-zinc-200 text-sm rounded-full hover:bg-stone-700/90 transition-all duration-200 hover:shadow-md\"\n                  >\n                    {skill}\n                  </span>\n                ))}\n              </div>\n            </div>\n          ))}\n        </div>\n      </section>\n    </PageTransition>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;;;AAHA;;;;AAKe,SAAS;;IACtB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAEzF,MAAM,eAAe,CAAC;QACpB,uBAAuB,CAAA,OAAQ,CAAC;gBAC9B,GAAG,IAAI;gBACP,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG;YACjB,CAAC;IACH;IAEA,MAAM,SAAS;QACb,WAAW;YAAC;YAAS;YAAc;YAAc;YAAU;YAAQ;SAAM;QACzE,WAAW;YACT;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;YAAC;YAAc;YAAY;YAAgB;SAAU;QACjE,wBAAwB;YACtB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;aACD;YACD,MAAM;QACR;QACA;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;aACD;YACD,MAAM;QACR;KACD;IAED,qBACE,6LAAC,wJAAA,CAAA,iBAAc;;0BAEb,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,6LAAC;wBAAE,WAAU;;4BAA4B;4BAEX;0CAC5B,6LAAC;gCAAK,WAAU;0CAA+C;;;;;;4BAEvD;4BAAI;4BACR;0CACJ,6LAAC;gCAAK,WAAU;0CAA+C;;;;;;4BAExD;;;;;;;kCAET,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;kCAIzC,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;0BAM3C,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCAGrD,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,SAAS;4BAAG,WAAW;wBAAO;;0CAEvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DACd,cAAA,6LAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;8DACL;;;;;;;;;;;0DAIH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;0EACd,6LAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0EACnG,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;0EAEvE,6LAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,6LAAC;wDAAK,WAAU;;0EACd,6LAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0EACnG,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;4DACjE;;;;;;;;;;;;;;;;;;;;;;;;;0CAMd,6LAAC;gCAAE,WAAU;0CAA6C;;;;;;;;;;;;;;;;;;0BAQ9D,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCAGrD,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,KAAK,IAAI,IAAI;4DACb,KAAK,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC;4DAC1B,WAAU;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACX,6LAAC;wEACD,WAAU;wEACV,SAAS,IAAM,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,UAAU;wEAC/C,UAAU;wEACV,MAAK;wEACL,cAAY,CAAC,KAAK,EAAE,IAAI,OAAO,CAAC,QAAQ,CAAC;wEACzC,WAAW,CAAA;4EACT,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;gFACxC,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,UAAU;4EAChC;wEACF;kFAEC,IAAI,OAAO;;;;;;kFAEd,6LAAC;wEACC,SAAS,IAAM,aAAa,IAAI,EAAE;wEAClC,WAAU;wEACV,cAAY,mBAAmB,CAAC,IAAI,EAAE,CAAC,GAAG,qBAAqB;kFAE9D,mBAAmB,CAAC,IAAI,EAAE,CAAC,iBAC1B,6LAAC,mNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;iGAErB,6LAAC,uNAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAI7B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;0FACd,6LAAC;gFAAI,WAAU;gFAA0B,MAAK;gFAAO,QAAO;gFAAe,SAAQ;gFAAY,OAAM;0FACnG,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;4EAEtE,IAAI,IAAI;;;;;;;kFAEX,6LAAC;wEAAK,WAAU;;0FACd,6LAAC;gFAAI,WAAU;gFAA0B,MAAK;gFAAO,QAAO;gFAAe,SAAQ;gFAAY,OAAM;0FACnG,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;4EAEtE,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;0DAKnB,6LAAC;gDACC,SAAS,IAAM,aAAa,IAAI,EAAE;gDAClC,WAAU;gDACV,cAAY,mBAAmB,CAAC,IAAI,EAAE,CAAC,GAAG,qBAAqB;0DAE9D,mBAAmB,CAAC,IAAI,EAAE,CAAC,iBAC1B,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAErB,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAI7B,6LAAC;wCAAE,WAAU;kDACV,IAAI,WAAW;;;;;;oCAIjB,mBAAmB,CAAC,IAAI,EAAE,CAAC,kBAC1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAG,WAAU;0DACX,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAClC,6LAAC;wDAAe,WAAU;kEAAQ;uDAAzB;;;;;;;;;;;;;;;;;+BA9EZ,IAAI,EAAE;;;;;;;;;;;;;;;;0BAyFnB,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCAErD,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,UAAU,UAAU,iBAChD,6LAAC;gCAAmB,WAAU;;kDAC5B,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,sBACd,6LAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;;;;;;;+BALH;;;;;;;;;;;;;;;;;;;;;;AAkBtB;GAzQwB;KAAA", "debugId": null}}]}