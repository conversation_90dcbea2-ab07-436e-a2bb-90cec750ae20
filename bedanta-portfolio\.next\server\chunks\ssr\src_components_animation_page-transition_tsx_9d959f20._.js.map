{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/animation/page-transition.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { ReactNode } from \"react\";\n\ninterface PageTransitionProps {\n  children: ReactNode;\n}\n\nexport function PageTransition({ children }: PageTransitionProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 10 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: 10 }}\n      transition={{\n        duration: 0.3,\n        ease: \"easeInOut\",\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG;QAAG;QAC1B,YAAY;YACV,UAAU;YACV,MAAM;QACR;kBAEC;;;;;;AAGP", "debugId": null}}]}