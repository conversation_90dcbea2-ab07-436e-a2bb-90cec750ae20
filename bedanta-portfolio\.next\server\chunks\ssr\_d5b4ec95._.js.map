{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx <module evaluation>\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport \"./globals.css\";\nimport { Header } from \"@/components/layout/header\";\nimport { Footer } from \"@/components/layout/footer\";\n\nexport const metadata: Metadata = {\n  title: \"Bedanta Kataki - Full Stack Developer Portfolio\",\n  description: \"<PERSON><PERSON><PERSON> is a Full Stack Developer and Computer Science student at NIT Silchar. Specializing in TypeScript, React, Node.js, and modern web technologies. View projects, experience, and contact information.\",\n  keywords: [\n    \"Bedanta Kataki\",\n    \"Full Stack Developer\",\n    \"TypeScript Developer\",\n    \"React Developer\",\n    \"Node.js Developer\",\n    \"Web Developer\",\n    \"NIT Silchar\",\n    \"Portfolio\",\n    \"Software Engineer\",\n    \"Frontend Developer\",\n    \"Backend Developer\",\n    \"MERN Stack\",\n    \"T3 Stack\",\n    \"GraphQL\",\n    \"System Design\"\n  ],\n  authors: [{ name: \"Bedanta Kataki\" }],\n  creator: \"Bedanta Kataki\",\n  publisher: \"Bedanta Kataki\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL('https://xbedanta.vercel.app'),\n  alternates: {\n    canonical: '/',\n  },\n  openGraph: {\n    title: \"Bedanta Kataki - Full Stack Developer Portfolio\",\n    description: \"Full Stack Developer and Electronics & Instrumentation student at NIT Silchar. Specializing in TypeScript, React, Node.js, and modern web technologies.\",\n    url: 'https://xbedanta.vercel.app',\n    siteName: 'Bedanta Kataki Portfolio',\n    images: [\n      {\n        url: '/me.jpeg',\n        width: 1200,\n        height: 630,\n        alt: 'Bedanta Kataki - Full Stack Developer',\n      },\n    ],\n    locale: 'en_US',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: \"Bedanta Kataki - Full Stack Developer Portfolio\",\n    description: \"Full Stack Developer and Electronics & Instrumentation student at NIT Silchar. Specializing in TypeScript, React, Node.js, and modern web technologies.\",\n    images: ['/me.jpeg'],\n    creator: '@bedantaxdev',\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n  verification: {\n    google: 'your-google-verification-code',\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <link rel=\"canonical\" href=\"https://xbedanta.vercel.app\" />\n        <meta name=\"author\" content=\"Bedanta Kataki\" />\n        <meta name=\"robots\" content=\"index, follow\" />\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              \"@context\": \"https://schema.org\",\n              \"@type\": \"Person\",\n              \"name\": \"Bedanta Kataki\",\n              \"jobTitle\": \"Full Stack Developer\",\n              \"description\": \"Full Stack Developer and Electronics & Instrumentation student at NIT Silchar\",\n              \"url\": \"https://xbedanta.vercel.app\",\n              \"image\": \"https://xbedanta.vercel.app/me.jpeg\",\n              \"email\": \"<EMAIL>\",\n              \"alumniOf\": {\n                \"@type\": \"CollegeOrUniversity\",\n                \"name\": \"National Institute of Technology, Silchar\"\n              },\n              \"knowsAbout\": [\n                \"TypeScript\",\n                \"React\",\n                \"Node.js\",\n                \"Full Stack Development\",\n                \"Web Development\",\n                \"System Design\",\n                \"GraphQL\",\n                \"MERN Stack\"\n              ],\n              \"sameAs\": [\n                \"https://github.com/r4inr3aper\",\n                \"https://leetcode.com/u/xbedanta/\",\n                \"https://x.com/bedantaxdev\",\n                \"https://www.linkedin.com/in/bedanta-kataki-0b5205257/\"\n              ]\n            })\n          }}\n        />\n      </head>\n      <body className=\"flex min-h-screen flex-col items-center justify-between\">\n        <main className=\"p-4 py-24 gap-6 w-full lg:w-[55%]\">\n          <Header />\n          <div className=\"flex flex-col gap-12 w-full h-full\">\n            {children}\n          </div>\n        </main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QAAC;YAAE,MAAM;QAAiB;KAAE;IACrC,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc,IAAI,IAAI;IACtB,YAAY;QACV,WAAW;IACb;IACA,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAW;QACpB,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAY,MAAK;;;;;;kCAC3B,8OAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAC5B,8OAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAC5B,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,QAAQ;gCACR,YAAY;gCACZ,eAAe;gCACf,OAAO;gCACP,SAAS;gCACT,SAAS;gCACT,YAAY;oCACV,SAAS;oCACT,QAAQ;gCACV;gCACA,cAAc;oCACZ;oCACA;oCACA;oCACA;oCACA;oCACA;oCACA;oCACA;iCACD;gCACD,UAAU;oCACR;oCACA;oCACA;oCACA;iCACD;4BACH;wBACF;;;;;;;;;;;;0BAGJ,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAK,WAAU;;0CACd,8OAAC,sIAAA,CAAA,SAAM;;;;;0CACP,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;kCAGL,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;AAIf", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}