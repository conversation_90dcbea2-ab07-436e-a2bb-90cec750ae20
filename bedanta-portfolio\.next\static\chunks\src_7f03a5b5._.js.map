{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/components/animation/page-transition.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { ReactNode } from \"react\";\n\ninterface PageTransitionProps {\n  children: ReactNode;\n}\n\nexport function PageTransition({ children }: PageTransitionProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 10 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: 10 }}\n      transition={{\n        duration: 0.3,\n        ease: \"easeInOut\",\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG;QAAG;QAC1B,YAAY;YACV,UAAU;YACV,MAAM;QACR;kBAEC;;;;;;AAGP;KAdgB", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/bedanta-portfolio/bedanta-portfolio/src/app/projects/page.tsx"], "sourcesContent": ["\"use client\";\nimport { PageTransition } from \"@/components/animation/page-transition\";\nimport { useState } from \"react\";\nimport { ExternalLink, Github, Code } from \"lucide-react\";\n\nexport default function Projects() {\n  const projects = [\n    {\n      id: 1,\n      name: \"Incandescence 2025 Official Website\",\n      description:\n        \"Developed a scalable game feature with photo uploads, letter collection and robust backend handling over 1.3M+ API requests.\",\n      image:\n        \"/incand.png\",\n      imageAlt: \"Screenshot of portfolio website homepage with dark theme\",\n      techStack: [\n        \"Next.js\",\n        \"TailwindCSS\",\n        \"Typescript\",\n        \"Express.js\",\n        \"MongoDB\",\n        \"Prisma\",\n        \"Node.js\",\n        \"Amazon S3\",\n        \"Git\",\n      ],\n      link: \"https://incand.in\",\n      github: \"https://github.com/gdsc-nits-org/incand-api-2025\",\n      featured: true,\n    },\n    {\n      id: 2,\n      name: \"Flick\",\n      description:\n        \"Fully functional e-commerce platform with user authentication, order management, promo code management and admin dashboard.\",\n      image:\n        \"/flick.png\",\n      imageAlt: \"E-commerce platform product listing page\",\n      techStack: [\n        \"React.js\",\n        \"SCSS\",\n        \"Express.js\",\n        \"MongoDB\",\n        \"Git\",\n        \"Node.js\",\n      ],\n      link: \"https://flick-puce.vercel.app/\",\n      github: \"https://github.com/r4inr3aper/Flick\",\n      featured: true,\n    },\n    {\n      \"id\": 3,\n      \"name\": \"Inframe School Official Website\",\n      \"description\": \"Built a custom CMS from scratch for efficient content management, ensuring easy updates for admins.\",\n      \"image\": \"/inframe-school.png\",\n      \"imageAlt\": \"Inframe School homepage interface with custom CMS\",\n      \"techStack\": [\n        \"Next.js\",\n        \"TailwindCSS\",\n        \"Typescript\",\n        \"Node.js\",\n        \"Express.js\",\n        \"MongoDB\",\n        \"JWT\",\n        \"Vercel\",\n        \"Git\"\n      ],\n      \"link\": \"https://www.inframeschool.com/\",\n      \"github\": \"\", \n      \"featured\": false\n    },\n    {\n      id: 4,\n      name: \"Tecnoesis 2024 Official Website\",\n      description:\n        \"Tech fest site built for high traffic. Developed complex GSAP animations, integrated multiple APIs and handled 1M+ backend requests for a seamless user experience.\",\n      image:\n        \"/tecnoesis.png\",\n      imageAlt: \"AI chat application interface showing conversation\",\n      techStack: [\n        \"Next.js\",\n        \"TailwindCSS\",\n        \"Typescript\",\n        \"GSAP\",\n        \"Express.js\",\n        \"MongoDB\",\n        \"Prisma\",\n        \"Node.js\",\n        \"Git\",\n      ],\n      link: \"https://tecnoesis-2024.pages.dev/\",\n      github: \"https://github.com/gdsc-nits-org/tecnoesis-2024\",\n      featured: false,\n    },\n    {\n      id: 5,\n      name: \"MERN Stack Chat App\",\n      description:\n        \"Real-time chat application featuring single & group chats with admin-controlled chat rooms and user management.\",\n      image:\n        \"https://assets-global.website-files.com/640f99c52b298c7753381c38/64227fdf55b3ddef95c18e5c_63eea96ea535cc41b88083ef_KkLQmdi5g7hcjsceNvVeC0XZ4B78p1wJWwRf2kHmgQE8vcYNhQeswTYpe06Pq_r3vRFMi6RDoslKP3r2XQXUtr6wq-O_W4ROZQF0mCtKlJ183XcHxcVKBfOHU9I8k9BFgrGnSgU5jJE2QrvZVus6BUI.png\",\n      imageAlt: \"Chat app interface showing group and private chat windows\",\n      techStack: [\n        \"React.js\",\n        \"Redux\",\n        \"Express.js\",\n        \"Socket.io\",\n        \"MongoDB\",\n        \"JWT\",\n      ],\n      github: \"https://github.com/r4inr3aper/MERN-Chat-App\",\n      featured: false,\n    },\n    {\n      id: 6,\n      name: \"E-Cell NIT Silchar Official Website\",\n      description:\n        \"Developed a responsive and interactive website for the Entrepreneurship Cell of NIT Silchar, featuring event management and member profiles.\",\n      image:\n        \"/image.png\",\n      imageAlt: \"E-Cell NIT Silchar official website homepage\",\n      techStack: [\n        \"React.js\",\n        \"Redux\",\n        \"Express.js\",\n        \"MongoDB\",\n        \"JWT\",\n        \"Git\",\n        \"Node.js\",\n      ],\n      link: \"https://ecellnits.org/\",\n      github: \"https://github.com/r4inr3aper/e-cell-website-22\",\n      featured: false,\n    },\n    {\n      \"id\": 7,\n      \"name\": \"Ulog – A Promise for Affinity\",\n      \"description\": \"Built a responsive, content-driven platform celebrating authentic Assamese heritage, featuring dynamic blog posts and rich media galleries.\",\n      \"image\": \"/ulog.png\",\n      \"imageAlt\": \"Homepage of Ulog – A Promise for Affinity\",\n      \"techStack\": [\n        \"Next.js\",\n        \"Tailwind CSS\",\n        \"Node.js\",\n        \"Vercel\",\n        \"Git\"\n      ],\n      \"link\": \"https://ulog-five.vercel.app/\",\n      \"github\": \"https://github.com/r4inr3aper/ulog-heritage-collective\",\n      \"featured\": false\n    }\n  ];\n\n  const [hoveredProject, setHoveredProject] = useState<number | null>(null);\n\n  return (\n    <PageTransition>\n      <section className=\"w-full flex flex-col justify-start px-1 mb-6\">\n        <h2 className=\"text-xl sm:text-2xl font-medium\">Projects ~</h2>\n      </section>\n\n      <section className=\"w-full grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {projects.map((project) => (\n          <div\n            key={project.id}\n            className={`border border-stone-800/90 rounded-lg overflow-hidden flex flex-col bg-stone-900/30 transition-all duration-300 hover:shadow-lg hover:shadow-stone-900/50 ${\n              project.featured ? \"ring-1 ring-stone-600/50\" : \"\"\n            }`}\n            onMouseEnter={() => setHoveredProject(project.id)}\n            onMouseLeave={() => setHoveredProject(null)}\n          >\n            <div className=\"relative w-full h-40 cursor-pointer overflow-hidden\">\n              <div className=\"absolute top-0 right-0 z-10 m-2\">\n                {project.featured && (\n                  <span className=\"px-2 py-0.5 bg-stone-800/80 text-xs rounded-md text-amber-400 border border-amber-500/30\">\n                    Featured\n                  </span>\n                )}\n              </div>\n              <img\n                src={project.image}\n                alt={project.imageAlt}\n                className=\"w-full h-full object-cover transition-transform duration-700 ease-in-out\"\n                style={{\n                  transform:\n                    hoveredProject === project.id ? \"scale(1.05)\" : \"scale(1)\",\n                }}\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-stone-900 to-transparent opacity-70\"></div>\n            </div>\n\n            <div className=\"p-4 flex flex-col gap-2 flex-grow\">\n              <h3 className=\"text-lg font-semibold text-zinc-100\">\n                {project.name}\n              </h3>\n              <p className=\"text-xs text-zinc-300 line-clamp-2\">\n                {project.description}\n              </p>\n\n              <div className=\"mt-2\">\n                <div className=\"flex flex-wrap gap-1.5 mb-3\">\n                  {project.techStack.map((tech, index) => (\n                    <div\n                      key={index}\n                      className=\"flex items-center gap-1 px-2 py-0.5 bg-stone-800/70 text-xs rounded-md text-zinc-300 border border-stone-700/50\"\n                    >\n                      <Code size={10} className=\"text-zinc-400\" />\n                      {tech}\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              <div className=\"flex gap-2 mt-auto pt-2 border-t border-stone-800/50\">\n                {project.link && (\n                  <a\n                    href={project.link}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"flex items-center gap-1 px-2.5 py-1 rounded-md bg-stone-700/80 text-xs font-medium text-zinc-100 relative no-underline duration-300 ease-in hover:bg-stone-600/60 border border-stone-600/30 mt-2\"\n                  >\n                    <ExternalLink size={12} />\n                    View Live\n                  </a>\n                )}\n                {project.github && (\n                  <a\n                    href={project.github}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"flex items-center gap-1 px-2.5 py-1 rounded-md bg-transparent text-xs font-medium text-zinc-300 relative no-underline duration-300 ease-in hover:bg-stone-800/80 border border-stone-700/30 mt-2\"\n                  >\n                    <Github size={12} />\n                    Code\n                  </a>\n                )}\n              </div>\n            </div>\n          </div>\n        ))}\n      </section>\n    </PageTransition>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;;;AAHA;;;;AAKe,SAAS;;IACtB,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM;YACN,aACE;YACF,OACE;YACF,UAAU;YACV,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aACE;YACF,OACE;YACF,UAAU;YACV,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,MAAM;YACN,QAAQ;YACR,eAAe;YACf,SAAS;YACT,YAAY;YACZ,aAAa;gBACX;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;YACR,UAAU;YACV,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,aACE;YACF,OACE;YACF,UAAU;YACV,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aACE;YACF,OACE;YACF,UAAU;YACV,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aACE;YACF,OACE;YACF,UAAU;YACV,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,MAAM;YACN,QAAQ;YACR,eAAe;YACf,SAAS;YACT,YAAY;YACZ,aAAa;gBACX;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;YACR,UAAU;YACV,YAAY;QACd;KACD;IAED,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,qBACE,6LAAC,wJAAA,CAAA,iBAAc;;0BACb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAG,WAAU;8BAAkC;;;;;;;;;;;0BAGlD,6LAAC;gBAAQ,WAAU;0BAChB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wBAEC,WAAW,CAAC,0JAA0J,EACpK,QAAQ,QAAQ,GAAG,6BAA6B,IAChD;wBACF,cAAc,IAAM,kBAAkB,QAAQ,EAAE;wBAChD,cAAc,IAAM,kBAAkB;;0CAEtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,QAAQ,kBACf,6LAAC;4CAAK,WAAU;sDAA2F;;;;;;;;;;;kDAK/G,6LAAC;wCACC,KAAK,QAAQ,KAAK;wCAClB,KAAK,QAAQ,QAAQ;wCACrB,WAAU;wCACV,OAAO;4CACL,WACE,mBAAmB,QAAQ,EAAE,GAAG,gBAAgB;wCACpD;;;;;;kDAEF,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,QAAQ,IAAI;;;;;;kDAEf,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAGtB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;;;;;;wDACzB;;mDAJI;;;;;;;;;;;;;;;kDAUb,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,IAAI,kBACX,6LAAC;gDACC,MAAM,QAAQ,IAAI;gDAClB,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,6LAAC,yNAAA,CAAA,eAAY;wDAAC,MAAM;;;;;;oDAAM;;;;;;;4CAI7B,QAAQ,MAAM,kBACb,6LAAC;gDACC,MAAM,QAAQ,MAAM;gDACpB,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,6LAAC,yMAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;oDAAM;;;;;;;;;;;;;;;;;;;;uBApEvB,QAAQ,EAAE;;;;;;;;;;;;;;;;AA+E3B;GA9OwB;KAAA", "debugId": null}}]}